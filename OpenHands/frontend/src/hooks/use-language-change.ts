import { useEffect } from "react";
import i18n from "#/i18n";
import { useSettings } from "./query/use-settings";

/**
 * Hook to handle language changes when settings are updated
 */
export const useLanguageChange = () => {
  const { data: settings } = useSettings();

  useEffect(() => {
    if (settings?.LANGUAGE && i18n.language !== settings.LANGUAGE) {
      i18n.changeLanguage(settings.LANGUAGE);
    }
  }, [settings?.LANGUAGE]);
};
