import i18n from "i18next";
import Backend from "i18next-http-backend";
import LanguageDetector from "i18next-browser-languagedetector";
import { initReactI18next } from "react-i18next";

export const AvailableLanguages = [
  { label: "English", value: "en" },
  { label: "日本語", value: "ja" },
  { label: "简体中文", value: "zh-CN" },
  { label: "繁體中文", value: "zh-TW" },
  { label: "한국어", value: "ko-KR" },
  { label: "Norsk", value: "no" },
  { label: "Arabic", value: "ar" },
  { label: "Deutsch", value: "de" },
  { label: "Français", value: "fr" },
  { label: "Italiano", value: "it" },
  { label: "Português", value: "pt" },
  { label: "Español", value: "es" },
  { label: "Türkçe", value: "tr" },
  { label: "Українська", value: "uk" },
];

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    // 设置默认语言为中文
    lng: "zh-CN",
    fallbackLng: "zh-CN",
    debug: import.meta.env.NODE_ENV === "development",

    // 支持的语言列表
    supportedLngs: ["en", "ja", "zh-CN", "zh-TW", "ko-KR", "no", "ar", "de", "fr", "it", "pt", "es", "tr", "uk"],

    // 语言检测配置 - 优先使用设置的语言
    detection: {
      order: ['localStorage', 'querystring', 'cookie', 'sessionStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage', 'cookie'],
      lookupLocalStorage: 'i18nextLng',
      lookupCookie: 'i18next',
      lookupSessionStorage: 'i18nextLng',
      lookupQuerystring: 'lng',
    },

    // 后端配置
    backend: {
      loadPath: '/locales/{{lng}}/translation.json',
    },

    // 预加载中文和英文
    preload: ['zh-CN', 'en'],

    // 其他配置
    load: 'currentOnly',
    cleanCode: true,
    nonExplicitSupportedLngs: true,
  });

export default i18n;
